using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service to manage gradual migration from existing services to enhanced services
/// Provides feature flags, rollback capabilities, and migration monitoring
/// </summary>
public sealed class EnhancedServicesMigrationService : IEnhancedServicesMigrationService
{
    private readonly ILogger<EnhancedServicesMigrationService> _logger;
    private readonly IOptionsMonitor<EnhancedServicesOptions> _options;
    private readonly Dictionary<string, MigrationStatus> _migrationStatus = new();
    private readonly object _statusLock = new();

    public EnhancedServicesMigrationService(
        ILogger<EnhancedServicesMigrationService> logger,
        IOptionsMonitor<EnhancedServicesOptions> options)
    {
        _logger = logger;
        _options = options;
        
        InitializeMigrationStatus();
    }

    /// <summary>
    /// Checks if enhanced data retrieval should be used
    /// </summary>
    public bool ShouldUseEnhancedDataRetrieval()
    {
        var options = _options.CurrentValue;
        var status = GetMigrationStatus("EnhancedDataRetrieval");
        
        return options.EnableEnhancedDataRetrieval && 
               status.IsEnabled && 
               !status.IsRolledBack;
    }

    /// <summary>
    /// Checks if adaptive rate limiting should be used
    /// </summary>
    public bool ShouldUseAdaptiveRateLimit()
    {
        var options = _options.CurrentValue;
        var status = GetMigrationStatus("AdaptiveRateLimit");
        
        return options.EnableAdaptiveRateLimit && 
               status.IsEnabled && 
               !status.IsRolledBack;
    }

    /// <summary>
    /// Checks if adaptive signal generation should be used
    /// </summary>
    public bool ShouldUseAdaptiveSignalGeneration()
    {
        var options = _options.CurrentValue;
        var status = GetMigrationStatus("AdaptiveSignalGeneration");
        
        return options.EnableAdaptiveSignalGeneration && 
               status.IsEnabled && 
               !status.IsRolledBack;
    }

    /// <summary>
    /// Enables a specific enhanced service
    /// </summary>
    public async Task EnableServiceAsync(string serviceName, string reason)
    {
        lock (_statusLock)
        {
            if (_migrationStatus.TryGetValue(serviceName, out var status))
            {
                status.IsEnabled = true;
                status.IsRolledBack = false;
                status.LastChangeTime = DateTime.UtcNow;
                status.LastChangeReason = reason;
                status.ChangeHistory.Add(new MigrationChange
                {
                    Timestamp = DateTime.UtcNow,
                    Action = "Enable",
                    Reason = reason
                });
            }
        }

        _logger.LogInformation("Enhanced service {ServiceName} enabled: {Reason}", serviceName, reason);
        await Task.CompletedTask;
    }

    /// <summary>
    /// Disables a specific enhanced service (rollback)
    /// </summary>
    public async Task DisableServiceAsync(string serviceName, string reason)
    {
        lock (_statusLock)
        {
            if (_migrationStatus.TryGetValue(serviceName, out var status))
            {
                status.IsEnabled = false;
                status.IsRolledBack = true;
                status.LastChangeTime = DateTime.UtcNow;
                status.LastChangeReason = reason;
                status.ChangeHistory.Add(new MigrationChange
                {
                    Timestamp = DateTime.UtcNow,
                    Action = "Disable",
                    Reason = reason
                });
            }
        }

        _logger.LogWarning("Enhanced service {ServiceName} disabled (rollback): {Reason}", serviceName, reason);
        await Task.CompletedTask;
    }

    /// <summary>
    /// Gets the current migration status for all services
    /// </summary>
    public MigrationReport GetMigrationReport()
    {
        lock (_statusLock)
        {
            var options = _options.CurrentValue;
            
            return new MigrationReport
            {
                GeneratedAt = DateTime.UtcNow,
                OverallStatus = CalculateOverallStatus(),
                ServiceStatuses = new Dictionary<string, MigrationStatus>(_migrationStatus),
                ConfigurationStatus = new ConfigurationStatus
                {
                    EnableEnhancedDataRetrieval = options.EnableEnhancedDataRetrieval,
                    EnableAdaptiveRateLimit = options.EnableAdaptiveRateLimit,
                    EnableAdaptiveSignalGeneration = options.EnableAdaptiveSignalGeneration,
                    EnableEnhancedMetrics = options.EnableEnhancedMetrics,
                    EnableSyntheticData = options.EnableSyntheticData,
                    EnableEmergencyMode = options.EnableEmergencyMode
                }
            };
        }
    }

    /// <summary>
    /// Performs a health check on enhanced services
    /// </summary>
    public async Task<HealthCheckResult> PerformHealthCheckAsync()
    {
        var result = new HealthCheckResult
        {
            CheckTime = DateTime.UtcNow,
            OverallHealth = HealthStatus.Healthy
        };

        try
        {
            // Check each enhanced service
            await CheckDataRetrievalHealthAsync(result);
            await CheckRateLimitingHealthAsync(result);
            await CheckSignalGenerationHealthAsync(result);

            // Determine overall health
            if (result.ServiceChecks.Any(c => c.Value.Status == HealthStatus.Unhealthy))
            {
                result.OverallHealth = HealthStatus.Unhealthy;
            }
            else if (result.ServiceChecks.Any(c => c.Value.Status == HealthStatus.Degraded))
            {
                result.OverallHealth = HealthStatus.Degraded;
            }

            _logger.LogInformation("Enhanced services health check completed: {Status}", result.OverallHealth);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            result.OverallHealth = HealthStatus.Unhealthy;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    private void InitializeMigrationStatus()
    {
        var services = new[]
        {
            "EnhancedDataRetrieval",
            "AdaptiveRateLimit", 
            "AdaptiveSignalGeneration"
        };

        foreach (var service in services)
        {
            _migrationStatus[service] = new MigrationStatus
            {
                ServiceName = service,
                IsEnabled = true, // Start enabled by default
                IsRolledBack = false,
                LastChangeTime = DateTime.UtcNow,
                LastChangeReason = "Initial setup",
                ChangeHistory = new List<MigrationChange>
                {
                    new()
                    {
                        Timestamp = DateTime.UtcNow,
                        Action = "Initialize",
                        Reason = "Service migration initialized"
                    }
                }
            };
        }
    }

    private MigrationStatus GetMigrationStatus(string serviceName)
    {
        lock (_statusLock)
        {
            return _migrationStatus.TryGetValue(serviceName, out var status) 
                ? status 
                : new MigrationStatus { ServiceName = serviceName, IsEnabled = false };
        }
    }

    private string CalculateOverallStatus()
    {
        var enabledCount = _migrationStatus.Values.Count(s => s.IsEnabled && !s.IsRolledBack);
        var totalCount = _migrationStatus.Count;
        var rolledBackCount = _migrationStatus.Values.Count(s => s.IsRolledBack);

        if (rolledBackCount > 0)
            return $"Partial (Rollbacks: {rolledBackCount})";
        
        if (enabledCount == totalCount)
            return "Fully Migrated";
        
        if (enabledCount == 0)
            return "Not Migrated";
        
        return $"Partial ({enabledCount}/{totalCount})";
    }

    private async Task CheckDataRetrievalHealthAsync(HealthCheckResult result)
    {
        var check = new ServiceHealthCheck
        {
            ServiceName = "EnhancedDataRetrieval",
            Status = HealthStatus.Healthy,
            CheckTime = DateTime.UtcNow
        };

        try
        {
            if (ShouldUseEnhancedDataRetrieval())
            {
                // Perform basic health check
                check.Details = "Enhanced data retrieval is enabled and operational";
            }
            else
            {
                check.Status = HealthStatus.Degraded;
                check.Details = "Enhanced data retrieval is disabled, using fallback";
            }
        }
        catch (Exception ex)
        {
            check.Status = HealthStatus.Unhealthy;
            check.Details = $"Health check failed: {ex.Message}";
        }

        result.ServiceChecks["EnhancedDataRetrieval"] = check;
        await Task.CompletedTask;
    }

    private async Task CheckRateLimitingHealthAsync(HealthCheckResult result)
    {
        var check = new ServiceHealthCheck
        {
            ServiceName = "AdaptiveRateLimit",
            Status = HealthStatus.Healthy,
            CheckTime = DateTime.UtcNow
        };

        try
        {
            if (ShouldUseAdaptiveRateLimit())
            {
                check.Details = "Adaptive rate limiting is enabled and operational";
            }
            else
            {
                check.Status = HealthStatus.Degraded;
                check.Details = "Adaptive rate limiting is disabled, using static limits";
            }
        }
        catch (Exception ex)
        {
            check.Status = HealthStatus.Unhealthy;
            check.Details = $"Health check failed: {ex.Message}";
        }

        result.ServiceChecks["AdaptiveRateLimit"] = check;
        await Task.CompletedTask;
    }

    private async Task CheckSignalGenerationHealthAsync(HealthCheckResult result)
    {
        var check = new ServiceHealthCheck
        {
            ServiceName = "AdaptiveSignalGeneration",
            Status = HealthStatus.Healthy,
            CheckTime = DateTime.UtcNow
        };

        try
        {
            if (ShouldUseAdaptiveSignalGeneration())
            {
                check.Details = "Adaptive signal generation is enabled and operational";
            }
            else
            {
                check.Status = HealthStatus.Degraded;
                check.Details = "Adaptive signal generation is disabled, using original generator";
            }
        }
        catch (Exception ex)
        {
            check.Status = HealthStatus.Unhealthy;
            check.Details = $"Health check failed: {ex.Message}";
        }

        result.ServiceChecks["AdaptiveSignalGeneration"] = check;
        await Task.CompletedTask;
    }
}

/// <summary>
/// Interface for enhanced services migration management
/// </summary>
public interface IEnhancedServicesMigrationService
{
    bool ShouldUseEnhancedDataRetrieval();
    bool ShouldUseAdaptiveRateLimit();
    bool ShouldUseAdaptiveSignalGeneration();
    Task EnableServiceAsync(string serviceName, string reason);
    Task DisableServiceAsync(string serviceName, string reason);
    MigrationReport GetMigrationReport();
    Task<HealthCheckResult> PerformHealthCheckAsync();
}
