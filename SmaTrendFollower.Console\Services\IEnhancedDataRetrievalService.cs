using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced data retrieval service interface with multi-tier fallback capabilities
/// </summary>
public interface IEnhancedDataRetrievalService
{
    /// <summary>
    /// Retrieves stock bars with comprehensive fallback strategy
    /// </summary>
    Task<DataRetrievalResult<IPage<IBar>>> GetStockBarsAsync(
        string symbol, 
        DateTime startDate, 
        DateTime endDate,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Batch retrieval with partial success handling
    /// </summary>
    Task<IDictionary<string, DataRetrievalResult<IPage<IBar>>>> GetStockBarsBatchAsync(
        IEnumerable<string> symbols,
        DateTime startDate,
        DateTime endDate,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of a data retrieval operation with detailed attempt information
/// </summary>
public sealed class DataRetrievalResult<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
    public string? ErrorMessage { get; set; }
    public List<DataRetrievalAttempt> Attempts { get; set; } = new();
    public TimeSpan TotalDuration { get; set; }
    public DataSource FinalDataSource { get; set; }
    public DataQuality DataQuality { get; set; }
}

/// <summary>
/// Information about a single data retrieval attempt
/// </summary>
public sealed class DataRetrievalAttempt
{
    public DataSource Source { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DataQuality DataQuality { get; set; }
    public TimeSpan Duration => EndTime - StartTime;
}

/// <summary>
/// Internal result for individual attempt operations
/// </summary>
internal sealed class AttemptResult<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
}

/// <summary>
/// Data source enumeration for tracking retrieval attempts
/// </summary>
public enum DataSource
{
    Unknown,
    PrimaryApi,
    CacheRelaxed,
    Synthetic,
    EmergencyCache
}

/// <summary>
/// Data quality levels for different sources
/// </summary>
public enum DataQuality
{
    Unknown,
    High,        // Fresh API data
    Medium,      // Recent cached data
    Low,         // Stale cached data
    Synthetic,   // Generated data
    Emergency    // Very stale emergency data
}
