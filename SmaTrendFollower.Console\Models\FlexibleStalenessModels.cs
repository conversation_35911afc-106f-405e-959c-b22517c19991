namespace SmaTrendFollower.Models;

/// <summary>
/// Result of flexible staleness validation with graduated responses
/// </summary>
public sealed class FlexibleStalenessResult
{
    public TimeSpan DataAge { get; set; }
    public DataType DataType { get; set; }
    public string DataSource { get; set; } = string.Empty;
    public bool IsMarketHours { get; set; }
    public StalenessPolicy Policy { get; set; }
    public DateTime ValidationTimestamp { get; set; }
    public StalenessContext? Context { get; set; }
    
    public StalenessLevel StalenessLevel { get; set; }
    public bool IsAcceptable { get; set; }
    public double QualityScore { get; set; }
    public RecommendedAction RecommendedAction { get; set; }
}

/// <summary>
/// Context information for staleness validation
/// </summary>
public sealed class StalenessContext
{
    /// <summary>
    /// Whether this is a critical operation that needs data regardless of staleness
    /// </summary>
    public bool IsCriticalOperation { get; set; }

    /// <summary>
    /// Whether fallback data is available if this data is rejected
    /// </summary>
    public bool HasFallbackData { get; set; }

    /// <summary>
    /// Whether this is a backfill operation (can tolerate staler data)
    /// </summary>
    public bool IsBackfillOperation { get; set; }

    /// <summary>
    /// Priority level of the operation (1-10, higher = more important)
    /// </summary>
    public int Priority { get; set; } = 5;

    /// <summary>
    /// Additional context information
    /// </summary>
    public string? AdditionalInfo { get; set; }
}

/// <summary>
/// Current status of the staleness service
/// </summary>
public sealed class StalenessStatus
{
    public StalenessPolicy CurrentPolicy { get; set; }
    public bool EmergencyModeActive { get; set; }
    public DateTime LastPolicyChange { get; set; }
    public bool IsMarketHours { get; set; }
}

/// <summary>
/// Staleness policy levels
/// </summary>
public enum StalenessPolicy
{
    /// <summary>
    /// Very strict staleness requirements
    /// </summary>
    Strict,

    /// <summary>
    /// Standard staleness requirements
    /// </summary>
    Standard,

    /// <summary>
    /// Relaxed staleness requirements
    /// </summary>
    Relaxed,

    /// <summary>
    /// Lenient staleness requirements
    /// </summary>
    Lenient,

    /// <summary>
    /// Emergency mode - accept almost any data
    /// </summary>
    Emergency
}

/// <summary>
/// Levels of data staleness
/// </summary>
public enum StalenessLevel
{
    /// <summary>
    /// Data is very fresh and current
    /// </summary>
    Fresh,

    /// <summary>
    /// Data is acceptable for most operations
    /// </summary>
    Acceptable,

    /// <summary>
    /// Data is stale but may be usable with caution
    /// </summary>
    Stale,

    /// <summary>
    /// Data is very stale and should be used only as last resort
    /// </summary>
    VeryStale,

    /// <summary>
    /// Data is ancient and should generally be rejected
    /// </summary>
    Ancient
}

/// <summary>
/// Recommended actions based on staleness validation
/// </summary>
public enum RecommendedAction
{
    /// <summary>
    /// Data is good to use without concerns
    /// </summary>
    UseData,

    /// <summary>
    /// Data can be used but log a warning
    /// </summary>
    UseDataWithWarning,

    /// <summary>
    /// Data can be used but exercise caution
    /// </summary>
    UseDataWithCaution,

    /// <summary>
    /// Only use data if no alternatives exist
    /// </summary>
    UseDataAsLastResort,

    /// <summary>
    /// Try to refresh the data
    /// </summary>
    RefreshData,

    /// <summary>
    /// Urgently refresh the data
    /// </summary>
    RefreshDataUrgently,

    /// <summary>
    /// Reject the data completely
    /// </summary>
    RejectData
}

/// <summary>
/// Staleness thresholds for different levels
/// </summary>
public sealed class StalenessThresholds
{
    public TimeSpan Fresh { get; set; }
    public TimeSpan Acceptable { get; set; }
    public TimeSpan Stale { get; set; }
    public TimeSpan VeryStale { get; set; }
}
