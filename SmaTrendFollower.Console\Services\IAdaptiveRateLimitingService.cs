namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for adaptive rate limiting service
/// </summary>
public interface IAdaptiveRateLimitingService
{
    /// <summary>
    /// Attempts to acquire a permit for an API call with adaptive rate limiting
    /// </summary>
    Task<RateLimitResult> TryAcquireAsync(
        string provider, 
        string operation, 
        int priority = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Releases a permit and records the result for adaptive adjustment
    /// </summary>
    void Release(string provider, string operation, bool success, TimeSpan duration, string? errorCode = null);

    /// <summary>
    /// Gets current rate limit statistics for a provider
    /// </summary>
    RateLimitStats GetStats(string provider);

    /// <summary>
    /// Creates an intelligent batch processor for multiple operations
    /// </summary>
    IBatchProcessor<T> CreateBatchProcessor<T>(
        string provider,
        Func<IEnumerable<T>, CancellationToken, Task<IDictionary<T, bool>>> batchOperation,
        BatchProcessorOptions? options = null);
}
